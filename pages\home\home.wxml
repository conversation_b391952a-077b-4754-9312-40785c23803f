<view class="home-page">
  <!-- 页面标题 -->
  <view class="page-title">
    <view class="title-main">山地果园</view>
    <view class="title-sub">智能监控系统</view>
  </view>

  <!-- 山地果园美景轮播图 -->
  <view class="swiper-container">
    <t-swiper
      current="{{current}}"
      autoplay="{{autoplay}}"
      duration="{{duration}}"
      interval="{{interval}}"
      navigation="{{navigation}}"
      imageProps="{{swiperImageProps}}"
      list="{{imgSrcs}}"
      bind:change="onSwiperChange"
    />
  </view>

  <!-- 快速数据概览 -->
  <view class="quick-stats">
    <view 
      class="stat-item"
      wx:for="{{quickStats}}"
      wx:key="label"
    >
      <view class="stat-value" style="color: {{item.color}}">{{item.value}}</view>
      <view class="stat-label">{{item.label}}</view>
    </view>
  </view>

  <!-- 功能介绍 -->
  <view class="feature-intro">
    <view class="intro-title">智能监控功能</view>
    <view class="intro-desc">通过底部导航栏快速访问各项功能</view>
    <view class="feature-list">
      <view class="feature-item">
        
        <text>实时数据监控</text>
      </view>
      <view class="feature-item">
        
        <text>智能预警提醒</text>
      </view>
      <view class="feature-item">
        
        <text>设备远程管理</text>
      </view>
      <view class="feature-item">
        
        <text>专家在线咨询</text>
      </view>
    </view>
  </view>

  <!-- 系统状态 -->
  <view class="system-status">
    <view class="status-header">
      <t-icon name="check-circle" size="32rpx" color="#4CAF50" />
      <text>系统运行正常</text>
    </view>
    <view class="status-time">最后更新：{{lastUpdateTime}}</view>
  </view>
</view>
