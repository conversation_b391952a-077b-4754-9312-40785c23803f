<view class="expert-page">
  <view class="page-header">
    <view class="title">专家咨询</view>
    <view class="subtitle">一键联系农技专家</view>
  </view>

  <view class="ai-assistant-banner">

    <view class="ai-content">
      <view class="ai-title">AI智能助手</view>
      <view class="ai-desc">常见问题即时解答，24小时在线服务</view>
    </view>
  </view>

  <view class="consultation-entry">
    <view class="entry-card" bindtap="startNewConsultation">

      <view class="entry-content">
        <view class="entry-title">发起新咨询</view>
        <view class="entry-desc">AI智能回复 + 专家人工服务</view>
      </view>
      <t-icon name="chevron-right" size="32rpx" color="#ccc" />
    </view>
  </view>

  <view class="quick-questions">
    <view class="section-title">常见问题</view>
    <view class="question-grid">
      <view 
        class="question-item"
        wx:for="{{quickQuestions}}"
        wx:key="*this"
        data-question="{{item}}"
        bindtap="onQuickQuestionTap"
      >
        {{item}}
      </view>
    </view>
  </view>

  <view class="expert-info">
    <view class="section-title">专家团队</view>
    <view class="expert-list">
      <view class="expert-item">
        <view class="expert-avatar">
          <t-icon name="user" size="40rpx" color="#4CAF50" />
        </view>
        <view class="expert-details">
          <view class="expert-name">李农技师</view>
          <view class="expert-specialty">果树栽培专家</view>
          <view class="expert-experience">15年经验</view>
        </view>
        <view class="expert-status online">在线</view>
      </view>
      
      <view class="expert-item">
        <view class="expert-avatar">
          <t-icon name="user" size="40rpx" color="#4CAF50" />
        </view>
        <view class="expert-details">
          <view class="expert-name">张专家</view>
          <view class="expert-specialty">病虫害防治</view>
          <view class="expert-experience">12年经验</view>
        </view>
        <view class="expert-status offline">离线</view>
      </view>
    </view>
  </view>

  <view class="consultation-history">
    <view class="section-title">咨询历史</view>
    <view class="history-list">
      <view 
        class="history-item"
        wx:for="{{consultationHistory}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onConsultationTap"
      >
        <view class="history-header">
          <view class="history-title">{{item.title}}</view>
          <view class="history-status {{item.status}}">
            {{item.status === 'answered' ? '已回复' : '待回复'}}
          </view>
        </view>
        <view class="history-info">
          <text class="expert-name">{{item.expert}}</text>
          <text class="history-time">{{item.time}}</text>
        </view>
        <view class="history-preview">{{item.preview}}</view>
      </view>
    </view>
  </view>

  <view class="upload-section">
    <view class="upload-tip">
      <t-icon name="info-circle" size="32rpx" color="#4CAF50" />
      <text>上传清晰的问题照片，有助于专家更准确诊断</text>
    </view>
    <view class="upload-btn" bindtap="uploadImage">
      <t-icon name="camera" size="40rpx" color="#4CAF50" />
      <text>上传照片</text>
    </view>
  </view>
</view>